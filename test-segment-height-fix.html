<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Segment Height Fix Test</title>
    <link rel="stylesheet" href="components/charts/snap-charts.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .chart-container {
            height: 300px;
            margin: 20px 0;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .test-data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>Segment Height Fix Test</h1>
    <p>Testing proportional segment heights between Stacked Column and Scrollable Stacked Column charts.</p>
    
    <div class="test-data">
        Test Data: Multiple months with US vs UK sales - US segments should be proportionally taller than UK segments in both charts
        <br>JAN: US (68) vs UK (32) | FEB: US (90) vs UK (60) | MAR: US (50) vs UK (30)
    </div>
    
    <div class="comparison">
        <div class="test-container">
            <h3>Stacked Column with Compare</h3>
            <div id="chart1" class="chart-container"></div>
        </div>
        
        <div class="test-container">
            <h3>Stacked Column Scrollable</h3>
            <div id="chart2" class="chart-container"></div>
        </div>
    </div>

    <script src="components/charts/snap-charts.js"></script>
    <script>
        // Test data with clear proportional differences
        const testData = [
            {
                month: 'JAN',
                day: '15',
                year: '24',
                sales: 100,
                royalties: 25,
                values: [68, 32], // US: 68, UK: 32 - should show clear proportional difference
                labels: ['US', 'UK']
            },
            {
                month: 'FEB',
                day: '15',
                year: '24',
                sales: 150,
                royalties: 35,
                values: [90, 60], // US: 90, UK: 60 - should show clear proportional difference
                labels: ['US', 'UK']
            },
            {
                month: 'MAR',
                day: '15',
                year: '24',
                sales: 80,
                royalties: 20,
                values: [50, 30], // US: 50, UK: 30 - should show clear proportional difference
                labels: ['US', 'UK']
            }
        ];

        // Initialize both chart types with identical data
        const chart1 = new SnapChart({
            container: '#chart1',
            type: 'stacked-column',
            data: testData,
            options: {
                responsive: true,
                animate: false // Disable animation for easier comparison
            }
        });

        const chart2 = new SnapChart({
            container: '#chart2',
            type: 'scrollable-stacked-column',
            data: testData,
            options: {
                responsive: true,
                animate: false // Disable animation for easier comparison
            }
        });

        console.log('Charts initialized with test data:', testData);
    </script>
</body>
</html>
